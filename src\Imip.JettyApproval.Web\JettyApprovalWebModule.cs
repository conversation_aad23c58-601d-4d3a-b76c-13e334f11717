using Imip.JettyApproval.EntityFrameworkCore;
using Imip.JettyApproval.Localization;
using Imip.JettyApproval.MultiTenancy;
using Imip.JettyApproval.Web.Menus;
using Imip.JettyApproval.Web.Middleware;
using Imip.JettyApproval.Web.Modules;
using Imip.JettyApproval.Web.Services.Attachments;
using Imip.JettyApproval.Web.Services.Interfaces;
using Imip.JettyApproval.Web.Services;
using Imip.JettyApproval.Web.Swagger;
using InertiaCore.Extensions;
using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using OpenIddict.Validation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Account.Web;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.AspNetCore.Mvc.Localization;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Toolbars;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Autofac;
using Volo.Abp.AutoMapper;
using Volo.Abp.Data;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using Volo.Abp.Identity.Web;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.PermissionManagement;
using Volo.Abp.Security.Claims;
using Volo.Abp.Studio.Client.AspNetCore;
using Volo.Abp.Swashbuckle;
using Volo.Abp.TenantManagement.Web;
using Volo.Abp.UI.Navigation;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.VirtualFileSystem;
using Volo.Abp.Caching.StackExchangeRedis;
using Volo.Abp.Caching;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using StackExchange.Redis;

namespace Imip.JettyApproval.Web;

[DependsOn(
    typeof(JettyApprovalHttpApiModule),
    typeof(JettyApprovalApplicationModule),
    typeof(JettyApprovalEntityFrameworkCoreModule),
    typeof(AbpAspNetCoreMvcModule),
    typeof(AbpAutofacModule),
    typeof(AbpStudioClientAspNetCoreModule),
    typeof(AbpIdentityWebModule),
    typeof(AbpAspNetCoreMvcUiLeptonXLiteThemeModule),
    typeof(AbpAccountWebModule),
    typeof(AbpTenantManagementWebModule),
    typeof(AbpFeatureManagementWebModule),
    typeof(AbpSwashbuckleModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(PermissionCheckerModule),
    typeof(AbpCachingStackExchangeRedisModule)
)]
public class JettyApprovalWebModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();

        context.Services.PreConfigure<AbpMvcDataAnnotationsLocalizationOptions>(options =>
        {
            options.AddAssemblyResource(
                typeof(JettyApprovalResource),
                typeof(JettyApprovalDomainModule).Assembly,
                typeof(JettyApprovalDomainSharedModule).Assembly,
                typeof(JettyApprovalApplicationModule).Assembly,
                typeof(JettyApprovalApplicationContractsModule).Assembly,
                typeof(JettyApprovalWebModule).Assembly
            );
        });
    }


    private void ConfigureDataProtection(ServiceConfigurationContext context, IConfiguration configuration, IWebHostEnvironment hostingEnvironment)
    {
        var appName = configuration["App:AppName"] ?? "Imip.JettyApproval";

        var dataProtectionBuilder = context.Services.AddDataProtection()
            .SetApplicationName(appName)
            .SetDefaultKeyLifetime(TimeSpan.FromDays(90));

        if (hostingEnvironment.IsDevelopment())
        {
            // Development: Local file system
            var keysPath = Path.Combine(hostingEnvironment.ContentRootPath, "App_Data", "DataProtection-Keys");
            Directory.CreateDirectory(keysPath);
            dataProtectionBuilder.PersistKeysToFileSystem(new DirectoryInfo(keysPath));
            Console.WriteLine($"Development: Data protection keys stored at: {keysPath}");
        }
        else
        {
            // Production/Kubernetes: Multiple storage strategies
            ConfigureProductionDataProtection(context, configuration, dataProtectionBuilder, appName);
        }
    }

    private void ConfigureProductionDataProtection(ServiceConfigurationContext context, IConfiguration configuration,
        IDataProtectionBuilder dataProtectionBuilder, string appName)
    {
        var redisIsEnabled = configuration.GetValue<bool>("Redis:IsEnabled", true);
        var redisConfiguration = configuration["Redis:Configuration"];

        context.Services.AddDataProtection()
            .PersistKeysToStackExchangeRedis(ConnectionMultiplexer.Connect(redisConfiguration), "DataProtection-Keys");

        // Strategy 1: Try Redis first (best for Kubernetes multi-pod scenarios)
        if (redisIsEnabled && !string.IsNullOrEmpty(redisConfiguration))
        {
            try
            {
                Configure<RedisCacheOptions>(options =>
                {
                    options.Configuration = redisConfiguration;
                    options.InstanceName = $"{appName}:DataProtection:";
                });
                Console.WriteLine("Production: Data protection keys stored in Redis (Kubernetes-ready)");

                Configure<AbpDistributedCacheOptions>(options =>
                {
                    options.KeyPrefix = $"{appName}:DataProtection:";
                });
                Console.WriteLine("Production: distributed cache stored in Redis (Kubernetes-ready)");
                return; // Redis success, we're done
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Redis data protection failed: {ex.Message}. Falling back to persistent volume.");
            }
        }

        // Strategy 2: Kubernetes Persistent Volume (fallback)
        ConfigureKubernetesPersistentStorage(dataProtectionBuilder);
    }

    private void ConfigureKubernetesPersistentStorage(IDataProtectionBuilder dataProtectionBuilder)
    {
        // Kubernetes persistent volume paths (must be configured in your deployment)
        var kubernetesPaths = new[]
        {
            "/app/data-protection-keys",  // Your current path
            "/data/keys",                 // Alternative common path
            "/persistent/data-protection-keys"  // Another alternative
        };

        foreach (var path in kubernetesPaths)
        {
            try
            {
                // Ensure directory exists
                Directory.CreateDirectory(path);

                // Test write permissions
                var testFile = Path.Combine(path, "test-write.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);

                dataProtectionBuilder.PersistKeysToFileSystem(new DirectoryInfo(path));
                Console.WriteLine($"Production: Data protection keys stored at: {path} (Kubernetes PV)");
                return; // Success
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to use path {path}: {ex.Message}");
            }
        }

        // Strategy 3: Fallback to temporary storage (NOT recommended for production)
        Console.WriteLine("WARNING: Using temporary storage for data protection keys. Users will be logged out on pod restarts!");
        var tempPath = Path.Combine(Path.GetTempPath(), "dataprotection-keys");
        Directory.CreateDirectory(tempPath);
        dataProtectionBuilder.PersistKeysToFileSystem(new DirectoryInfo(tempPath));
    }

    // Add this method to handle distributed cache configuration
    private void ConfigureDistributedCache(ServiceConfigurationContext context, IConfiguration configuration)
    {
        var redisIsEnabled = configuration.GetValue<bool>("Redis:IsEnabled", true);
        var redisConfiguration = configuration["Redis:Configuration"];

        Configure<AbpDistributedCacheOptions>(options =>
        {
            options.KeyPrefix = "Imip.JettyApproval.Cache:";
            options.GlobalCacheEntryOptions.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(24);
            options.GlobalCacheEntryOptions.SlidingExpiration = TimeSpan.FromHours(1);
        });

        if (redisIsEnabled && !string.IsNullOrEmpty(redisConfiguration))
        {
            try
            {
                // Redis cache will be configured by AbpCachingStackExchangeRedisModule
                Console.WriteLine("ABP distributed cache configured to use Redis");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Redis cache configuration failed: {ex.Message}. Using in-memory cache.");
            }
        }
        else
        {
            Console.WriteLine("Using in-memory distributed cache");
        }
    }

    private void ConfigureAntiforgery(ServiceConfigurationContext context, IConfiguration configuration, IWebHostEnvironment hostingEnvironment)
    {
        // Configure ABP antiforgery options
        Configure<AbpAntiForgeryOptions>(options =>
        {
            options.TokenCookie.Expiration = TimeSpan.FromDays(365);
            // In development, allow non-HTTPS for easier debugging
            if (hostingEnvironment.IsDevelopment())
            {
                options.TokenCookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
            }
        });

        // Configure ASP.NET Core antiforgery options
        context.Services.Configure<AntiforgeryOptions>(options =>
        {
            options.Cookie.Name = ".Imip.JettyApproval.Antiforgery";
            options.Cookie.HttpOnly = true;

            var requireHttps = !hostingEnvironment.IsDevelopment() &&
                              configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", true);

            options.Cookie.SecurePolicy = requireHttps ? CookieSecurePolicy.Always : CookieSecurePolicy.SameAsRequest;
            options.Cookie.SameSite = SameSiteMode.Lax;
            options.Cookie.MaxAge = TimeSpan.FromHours(2); // Longer lifetime for development
            options.FormFieldName = "__RequestVerificationToken";
            options.HeaderName = "X-CSRF-TOKEN";
        });
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();
        var appName = configuration["App:AppName"] ?? "Imip.IdentityServer";

        // Add this configuration for handling larger headers
        //context.Services.Configure<IISServerOptions>(static options =>
        //{
        //    options.MaxRequestHeadersTotalSize = 65536; // Increased header size limit (64KB)
        //});

        // For Kestrel
        context.Services.Configure<KestrelServerOptions>(options =>
        {
            options.Limits.MaxRequestHeadersTotalSize = 65536; // Increased header size limit (64KB)
        });

        ConfigureDataProtection(context, configuration, hostingEnvironment);
        ConfigureDistributedCache(context, configuration);

        if (!configuration.GetValue<bool>("App:DisablePII"))
        {
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.LogCompleteSecurityArtifact = true;
        }

        if (!configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata"))
        {
            // Remove OpenIddict server options since we're not using OpenIddict as a server
            // Configure<OpenIddictServerAspNetCoreOptions>(options =>
            // {
            //     options.DisableTransportSecurityRequirement = true;
            // });

            Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders = ForwardedHeaders.XForwardedProto;
            });
        }

        ConfigureAntiforgery(context, configuration, hostingEnvironment);

        context.Services.AddInertia(options =>
        {
            options.RootView = "~/Views/App.cshtml";
        });

        context.Services.AddViteHelper(options =>
        {
            options.PublicDirectory = "wwwroot";
            options.ManifestFilename = "manifest.json";
            options.BuildDirectory = "build";
        });

        ConfigureBundles();
        ConfigureUrls(configuration);
        ConfigureHealthChecks(context);
        ConfigureAuthentication(context, configuration);
        ConfigureAutoMapper();
        ConfigureVirtualFileSystem(hostingEnvironment);
        ConfigureNavigationServices();
        ConfigureAutoApiControllers();
        ConfigureSwaggerServices(context.Services);

        Configure<PermissionManagementOptions>(options =>
        {
            options.IsDynamicPermissionStoreEnabled = true;
        });

        // Register the HttpContextAccessor if it's not already registered
        context.Services.AddHttpContextAccessor();

        // Register HttpClientFactory for testing and other HTTP operations
        context.Services.AddHttpClient();

        // Register token service
        context.Services.AddScoped<ITokenService, TokenService>();
        context.Services.AddScoped<ExternalApiService>();
        context.Services.AddScoped<AppToAppService>();

        // Register background token refresh service
        context.Services.AddHostedService<BackgroundTokenRefreshService>();

        // Configure session for deferred user synchronization
        context.Services.AddSession(options =>
        {
            options.IdleTimeout = TimeSpan.FromMinutes(30);
            options.Cookie.HttpOnly = true;
            options.Cookie.IsEssential = true;
            options.Cookie.Name = ".Imip.JettyApproval.Session";
        });
    }

    private void ConfigureHealthChecks(ServiceConfigurationContext context)
    {
        context.Services.AddHealthChecks();
    }

    private void ConfigureBundles()
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options.StyleBundles.Configure(
                LeptonXLiteThemeBundles.Styles.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-styles.css");
                }
            );

            options.ScriptBundles.Configure(
                LeptonXLiteThemeBundles.Scripts.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-scripts.js");
                }
            );
        });
    }

    private void ConfigureUrls(IConfiguration configuration)
    {
        Configure<AppUrlOptions>(options =>
        {
            options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"];
        });
    }

    private void ConfigureAuthentication(ServiceConfigurationContext context, IConfiguration configuration)
    {
        context.Services.AddAuthentication(options =>
        {
            options.DefaultScheme = "Cookies";
            options.DefaultChallengeScheme = "oidc";
            options.DefaultSignOutScheme = "oidc";
        })
        .AddCookie("Cookies", options =>
        {
            options.ExpireTimeSpan = TimeSpan.FromMinutes(60);
            options.SlidingExpiration = true;
            options.Cookie.Name = ".Imip.JettyApproval.Auth";
            options.Cookie.SameSite = SameSiteMode.Lax;
            options.LoginPath = "/Account/Login";
            options.LogoutPath = "/Account/Logout";

            // Add debugging for cookie events
            options.Events.OnRedirectToLogin = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                return Task.CompletedTask;
            };

            options.Events.OnRedirectToAccessDenied = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                return Task.CompletedTask;
            };
        })
        .AddJwtBearer("Bearer", options =>
        {
            // Configure JWT Bearer for validating tokens from other apps
            options.Authority = configuration["AuthServer:Authority"];
            options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", false);
            options.Audience = configuration["AuthServer:ClientId"]; // Use this app's client ID as audience

            // Configure token validation parameters
            options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = configuration["AuthServer:Authority"],
                ValidAudience = configuration["AuthServer:ClientId"],
                ClockSkew = TimeSpan.FromMinutes(5)
            };

            // Handle JWT events for debugging and custom validation
            options.Events = new JwtBearerEvents
            {
                OnTokenValidated = async context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                    var userManager = context.HttpContext.RequestServices.GetRequiredService<IdentityUserManager>();

                    try
                    {
                        var claimsPrincipal = context.Principal;
                        var email = claimsPrincipal.FindFirst("email")?.Value ??
                                   claimsPrincipal.FindFirst(ClaimTypes.Email)?.Value;
                        var sub = claimsPrincipal.FindFirst("sub")?.Value ??
                                 claimsPrincipal.FindFirst(ClaimTypes.NameIdentifier)?.Value;

                        // Find the user in our system
                        if (!string.IsNullOrEmpty(email))
                        {
                            var user = await userManager.FindByEmailAsync(email);
                            if (user != null)
                            {
                                // Add ABP-specific claims to the principal
                                var identity = (ClaimsIdentity)context.Principal.Identity;

                                // Remove existing ABP claims to avoid duplicates
                                var existingAbpClaims = identity.Claims
                                    .Where(c => c.Type.StartsWith("abp_") ||
                                               c.Type == AbpClaimTypes.UserId ||
                                               c.Type == AbpClaimTypes.UserName)
                                    .ToList();

                                foreach (var claim in existingAbpClaims)
                                {
                                    identity.RemoveClaim(claim);
                                }

                                // Add fresh ABP claims
                                identity.AddClaim(new Claim(AbpClaimTypes.UserId, user.Id.ToString()));
                                identity.AddClaim(new Claim(AbpClaimTypes.UserName, user.UserName));
                                identity.AddClaim(new Claim(AbpClaimTypes.Email, user.Email));
                            }
                            else
                            {
                                logger.LogWarning("JWT token validated but user not found in local system: {Email}", email);
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        logger.LogError(ex, "Error processing JWT token validation");
                        context.Fail("Token validation failed");
                    }
                },
                OnAuthenticationFailed = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                    logger.LogError("JWT authentication failed: {Exception}", context.Exception);
                    return Task.CompletedTask;
                },
                OnChallenge = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                    return Task.CompletedTask;
                }
            };
        })
        .AddOpenIdConnect("oidc", options =>
        {
            // Use consistent configuration keys - pick one set
            options.Authority = configuration["AuthServer:Authority"] ?? configuration["OpenIdConnect:Authority"];
            options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", false);

            options.ClientId = configuration["AuthServer:ClientId"] ?? configuration["OpenIdConnect:ClientId"];
            options.ClientSecret = configuration["AuthServer:ClientSecret"] ?? configuration["OpenIdConnect:ClientSecret"];

            // Configure redirect URIs
            options.SignedOutRedirectUri = configuration["OpenIdConnect:SignedOutRedirectUri"] ?? "http://localhost:5000";
            options.SignedOutCallbackPath = "/signout-callback-oidc";
            options.RemoteSignOutPath = "/signout-oidc";

            // Configure sign-in scheme to use cookies
            options.SignInScheme = "Cookies";

            // Use authorization code flow with PKCE for security
            options.ResponseType = "code";
            options.UsePkce = false;

            // Basic scopes
            options.Scope.Clear();
            options.Scope.Add("openid");
            options.Scope.Add("profile");
            options.Scope.Add("email");

            // Save tokens and get claims from UserInfo
            options.SaveTokens = true;
            options.GetClaimsFromUserInfoEndpoint = true;

            // Configure token refresh
            options.RefreshInterval = TimeSpan.FromMinutes(30); // Refresh every 30 minutes
            options.UseTokenLifetime = true; // Use token lifetime from identity server

            // Map claims properly
            options.ClaimActions.MapJsonKey(ClaimTypes.NameIdentifier, "sub");
            options.ClaimActions.MapJsonKey(ClaimTypes.Name, "name");
            options.ClaimActions.MapJsonKey(ClaimTypes.Email, "email");
            options.ClaimActions.MapJsonKey(ClaimTypes.Role, "role");

            // Handle events
            options.Events.OnTokenValidated = async context =>
            {
                try
                {
                    var serviceProvider = context.HttpContext.RequestServices;
                    var userManager = serviceProvider.GetRequiredService<IdentityUserManager>();
                    var currentTenant = serviceProvider.GetRequiredService<ICurrentTenant>();
                    var guidGenerator = serviceProvider.GetRequiredService<IGuidGenerator>();
                    var logger = serviceProvider.GetRequiredService<ILogger<JettyApprovalWebModule>>();

                    var externalUser = context.Principal;
                    var userId = externalUser.FindFirst("sub")?.Value ?? externalUser.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                    var email = externalUser.FindFirst("email")?.Value ?? externalUser.FindFirst(ClaimTypes.Email)?.Value;
                    var name = externalUser.FindFirst("name")?.Value ?? externalUser.FindFirst(ClaimTypes.Name)?.Value;

                    if (string.IsNullOrEmpty(email))
                    {
                        logger.LogError("No email claim found in external user claims");
                        context.Fail("Email claim is required");
                        return;
                    }

                    // Find or create user in ABP
                    // First try to find by email (more reliable)
                    var user = await userManager.FindByEmailAsync(email);

                    if (user == null)
                    {
                        logger.LogInformation("Creating new user for email: {Email}, external ID: {ExternalId}", email, userId);


                        // Parse userId string to Guid
                        if (!Guid.TryParse(userId, out var userGuid))
                        {
                            logger.LogError("Invalid user ID format: {UserId}", userId);
                            context.Fail("Invalid user ID format");
                            return;
                        }

                        user = new IdentityUser(
                            userGuid,
                            email,
                            email,
                            tenantId: currentTenant.Id
                        );

                        if (!string.IsNullOrEmpty(name))
                        {
                            user.Name = name;
                            user.SetProperty("DisplayName", name);
                        }

                        // Store the external user ID for future reference
                        if (!string.IsNullOrEmpty(userId))
                        {
                            user.SetProperty("ExternalUserId", userId);
                        }

                        user.SetEmailConfirmed(true); // Since it's from SSO, consider email verified

                        var result = await userManager.CreateAsync(user);
                        if (!result.Succeeded)
                        {
                            logger.LogError("Failed to create user: {Errors}", string.Join(", ", result.Errors.Select(e => e.Description)));
                            context.Fail("Failed to create user");
                            return;
                        }
                    }
                    else
                    {
                        // Update existing user information if needed
                        var needsUpdate = false;

                        if (!string.IsNullOrEmpty(name) && user.Name != name)
                        {
                            user.Name = name;
                            user.SetProperty("DisplayName", name);
                            needsUpdate = true;
                        }

                        // Update external user ID if it changed
                        var currentExternalId = user.GetProperty<string>("ExternalUserId");
                        if (!string.IsNullOrEmpty(userId) && currentExternalId != userId)
                        {
                            user.SetProperty("ExternalUserId", userId);
                            needsUpdate = true;
                        }

                        if (needsUpdate)
                        {
                            await userManager.UpdateAsync(user);
                        }
                    }

                    // Add ABP-specific claims
                    var identity = (ClaimsIdentity)context.Principal.Identity;

                    // Remove existing ABP claims to avoid duplicates
                    var existingAbpClaims = identity.Claims
                        .Where(c => c.Type.StartsWith("abp_") ||
                                   c.Type == AbpClaimTypes.UserId ||
                                   c.Type == AbpClaimTypes.UserName)
                        .ToList();

                    foreach (var claim in existingAbpClaims)
                    {
                        identity.RemoveClaim(claim);
                    }

                    // Add fresh ABP claims
                    identity.AddClaim(new Claim(AbpClaimTypes.UserId, user.Id.ToString()));
                    identity.AddClaim(new Claim(AbpClaimTypes.UserName, user.UserName));
                    identity.AddClaim(new Claim(AbpClaimTypes.Email, user.Email));

                    if (currentTenant.Id.HasValue)
                    {
                        identity.AddClaim(new Claim(AbpClaimTypes.TenantId, currentTenant.Id.ToString()));
                    }

                    logger.LogInformation("Successfully validated token for user: {Email}", email);

                    // Store tokens for later use
                    if (context.Properties?.Items.ContainsKey(".Token.access_token") == true)
                    {
                        var accessToken = context.Properties.Items[".Token.access_token"];
                        var refreshToken = context.Properties.Items.ContainsKey(".Token.refresh_token")
                            ? context.Properties.Items[".Token.refresh_token"]
                            : null;

                        logger.LogInformation("Access token stored for user: {Email}", email);

                        // You can store tokens in user properties or session for later use
                        user.SetProperty("AccessToken", accessToken);
                        if (!string.IsNullOrEmpty(refreshToken))
                        {
                            user.SetProperty("RefreshToken", refreshToken);
                        }

                        // Update user with token information
                        await userManager.UpdateAsync(user);
                    }
                }
                catch (Exception ex)
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                    logger.LogError(ex, "Error during token validation");
                    context.Fail("Token validation failed");
                }
            };

            // Handle sign-in errors
            options.Events.OnRemoteFailure = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                logger.LogError("Remote authentication failure: {Error}", context.Failure?.Message);

                context.Response.Redirect("/Account/Login?error=" + Uri.EscapeDataString(context.Failure?.Message ?? "Authentication failed"));
                context.HandleResponse();
                return Task.CompletedTask;
            };

            // Handle tenant information in redirect (if using multi-tenancy)
            options.Events.OnRedirectToIdentityProvider = context =>
            {
                var currentTenant = context.HttpContext.RequestServices.GetRequiredService<ICurrentTenant>();

                if (currentTenant.Id.HasValue && !string.IsNullOrEmpty(currentTenant.Name))
                {
                    context.ProtocolMessage.SetParameter("tenant", currentTenant.Name);
                }

                return Task.CompletedTask;
            };

            // Handle authentication errors
            options.Events.OnAuthenticationFailed = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                logger.LogError(context.Exception, "OIDC authentication failed");
                return Task.CompletedTask;
            };

            // Ensure user is signed in to the cookie scheme
            options.Events.OnTicketReceived = async context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                logger.LogInformation("OIDC ticket received successfully");

                // Log authentication details
                if (context.Principal?.Identity?.IsAuthenticated == true)
                {
                    logger.LogInformation("User is authenticated: {UserName}", context.Principal.Identity.Name);
                    logger.LogInformation("Authentication type: {AuthType}", context.Principal.Identity.AuthenticationType);

                    // Log all claims for debugging
                    foreach (var claim in context.Principal.Claims)
                    {
                        logger.LogInformation("Claim: {Type} = {Value}", claim.Type, claim.Value);
                    }

                    logger.LogInformation("OIDC authentication completed successfully");
                }
                else
                {
                    logger.LogWarning("User is not authenticated after ticket received");
                }
            };

            // Add more event handlers for debugging
            options.Events.OnAuthorizationCodeReceived = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                logger.LogInformation("Authorization code received");
                return Task.CompletedTask;
            };

            options.Events.OnTokenResponseReceived = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                logger.LogInformation("Token response received");
                return Task.CompletedTask;
            };

            options.Events.OnUserInformationReceived = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                logger.LogInformation("User information received");
                return Task.CompletedTask;
            };
        });

        context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = true;
        });

        context.Services.Configure<JwtBearerOptions>(options =>
        {
            options.Authority = configuration["AuthServer:Authority"];
            options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata");
            options.Audience = "JettyApproval";
        });
    }

    private void ConfigureAutoMapper()
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<JettyApprovalWebModule>();
        });
    }

    private static bool IsRunningInContainer()
    {
        // Check for container-specific environment indicators
        return File.Exists("/.dockerenv") ||
               (File.Exists("/proc/1/cgroup") && File.ReadAllText("/proc/1/cgroup").Contains("/docker/"));
    }

    private void ConfigureVirtualFileSystem(IWebHostEnvironment hostingEnvironment)
    {
        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<JettyApprovalWebModule>();

            // Only replace embedded resources with physical files in a development environment,
            // and only if we're not running in a container
            if (hostingEnvironment.IsDevelopment() && !IsRunningInContainer())
            {
                try
                {
                    var domainSharedPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Domain.Shared", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainSharedPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalDomainSharedModule>(domainSharedPath);
                    }

                    var domainPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Domain", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalDomainModule>(domainPath);
                    }

                    var contractsPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Application.Contracts", Path.DirectorySeparatorChar));
                    if (Directory.Exists(contractsPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalApplicationContractsModule>(
                            contractsPath);
                    }

                    var appPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Application", Path.DirectorySeparatorChar));
                    if (Directory.Exists(appPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalApplicationModule>(appPath);
                    }

                    var httpApiPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}..{0}src{0}Imip.JettyApproval.HttpApi", Path.DirectorySeparatorChar));
                    if (Directory.Exists(httpApiPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalHttpApiModule>(httpApiPath);
                    }

                    if (Directory.Exists(hostingEnvironment.ContentRootPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalWebModule>(hostingEnvironment
                            .ContentRootPath);
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception but continue without replacing embedded resources
                    Console.WriteLine($"Error configuring virtual file system: {ex.Message}");
                }
            }
        });
    }

    private void ConfigureNavigationServices()
    {
        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new JettyApprovalMenuContributor());
        });

        Configure<AbpToolbarOptions>(options =>
        {
            options.Contributors.Add(new JettyApprovalToolbarContributor());
        });
    }

    private void ConfigureAutoApiControllers()
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(JettyApprovalApplicationModule).Assembly);
        });
    }

    private void ConfigureSwaggerServices(IServiceCollection services)
    {
        services.AddAbpSwaggerGen(
            options =>
            {
                options.SwaggerDoc("v1", new OpenApiInfo { Title = "JettyApproval API", Version = "v1" });
                // Configure Swagger to handle file uploads with IFormFile
                options.OperationFilter<SwaggerFileOperationFilter>();

                // Add support for multipart/form-data
                options.MapType<IFormFile>(() => new OpenApiSchema
                {
                    Type = "string",
                    Format = "binary"
                });

                // Add support for FileUploadFormDto
                options.MapType<FileUploadFormDto>(() => new OpenApiSchema
                {
                    Type = "object",
                    Properties = new Dictionary<string, OpenApiSchema>
                    {
                        ["File"] = new OpenApiSchema { Type = "string", Format = "binary" },
                        ["Description"] = new OpenApiSchema { Type = "string" },
                        ["ReferenceId"] = new OpenApiSchema { Type = "string", Format = "uuid" },
                        ["ReferenceType"] = new OpenApiSchema { Type = "string" }
                    },
                    Required = new HashSet<string> { "File" }
                });

                options.DocInclusionPredicate((docName, description) =>
                {
                    // Check if the controller name contains "Abp" (for ABP framework controllers)
                    if (description.ActionDescriptor != null &&
                        description.ActionDescriptor.RouteValues != null &&
                        description.ActionDescriptor.RouteValues.TryGetValue("controller", out var controllerName) &&
                        controllerName != null &&
                        (controllerName.Contains("Abp") ||
                         controllerName.StartsWith("Abp")))
                    {
                        if (description.RelativePath != null &&
                            (description.RelativePath.Contains("application-configuration") ||
                             description.RelativePath.Contains("api/abp/application-configuration")))
                        {
                            return true; // Exclude application-configuration API
                        }

                        if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/tenant") ||
                         description.RelativePath.Contains("/tenants")))
                        {
                            return true; // Exclude tenant-related APIs
                        }

                        // Check if the relative path contains "account" or "accounts"
                        if (description.RelativePath != null &&
                            (description.RelativePath.Contains("/account") ||
                             description.RelativePath.Contains("/accounts")))
                        {
                            return true; // Exclude account-related APIs
                        }

                        // Exclude ABP framework controllers
                        return false;
                    }


                    // Check if the API is from the Identity module
                    if (description.GroupName != null && description.GroupName.Contains("Identity"))
                    {
                        return false; // Exclude identity management APIs
                    }

                    if (description.GroupName != null && description.GroupName.Contains("PermissionManagement"))
                    {
                        return false; // Exclude permission management APIs
                    }

                    if (description.GroupName != null && description.GroupName.Contains("FeatureManagement"))
                    {
                        return false; // Exclude feature management APIs
                    }

                    if (description.GroupName != null && description.GroupName.Contains("SettingManagement"))
                    {
                        return false; // Exclude feature management APIs
                    }

                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/permission") ||
                         description.RelativePath.Contains("/permissions")))
                    {
                        return false; // Exclude account-related APIs
                    }

                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/feature") ||
                         description.RelativePath.Contains("/features")))
                    {
                        return false; // Exclude feature-related APIs
                    }

                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/setting") ||
                         description.RelativePath.Contains("/settings")))
                    {
                        return false; // Exclude feature-related APIs
                    }

                    // Check if the relative path contains identity-related endpoints
                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("/identity") ||
                         description.RelativePath.Contains("/identities") ||
                         description.RelativePath.Contains("/users") ||
                         description.RelativePath.Contains("/roles")))
                    {
                        return false; // Exclude identity-related APIs
                    }

                    // Check if the API is from the AbpApiDefinition controller
                    if (description.ActionDescriptor != null &&
                        description.ActionDescriptor.DisplayName != null &&
                        description.ActionDescriptor.DisplayName.Contains("AbpApiDefinition"))
                    {
                        return false; // Exclude AbpApiDefinition API
                    }

                    // Check if the API is from the AbpApplicationConfiguration controller
                    //    if (description.ActionDescriptor != null &&
                    //        description.ActionDescriptor.DisplayName != null &&
                    //        description.ActionDescriptor.DisplayName.Contains("AbpApplicationConfiguration"))
                    //    {
                    //        return false; // Exclude AbpApplicationConfiguration API
                    //    }

                    // Check if the API is from the AbpApplicationLocalization controller
                    if (description.ActionDescriptor != null &&
                        description.ActionDescriptor.DisplayName != null &&
                        description.ActionDescriptor.DisplayName.Contains("AbpApplicationLocalization"))
                    {
                        return false; // Exclude AbpApplicationLocalization API
                    }

                    // Check if the relative path contains application-configuration
                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("api-definition") ||
                         description.RelativePath.Contains("api/abp/api-definition")))
                    {
                        return false; // Exclude api-definition API
                    }

                    // Check if the relative path contains application-configuration
                    //    if (description.RelativePath != null &&
                    //        (description.RelativePath.Contains("application-configuration") ||
                    //         description.RelativePath.Contains("api/abp/application-configuration")))
                    //    {
                    //        return false; // Exclude application-configuration API
                    //    }

                    // Check if the relative path contains application-localization
                    if (description.RelativePath != null &&
                        (description.RelativePath.Contains("application-localization") ||
                         description.RelativePath.Contains("api/abp/application-localization")))
                    {
                        return false; // Exclude application-localization API
                    }

                    return true; // Include all other APIs
                });

                options.CustomSchemaIds(type =>
                {
                    // Handle generic types
                    if (type.IsGenericType)
                    {
                        var prefix = type.Name.Split('`')[0];
                        var genericArgs = string.Join("And", type.GetGenericArguments().Select(t =>
                        {
                            if (t.IsGenericType)
                            {
                                var nestedPrefix = t.Name.Split('`')[0];
                                var nestedArgs = string.Join("And", t.GetGenericArguments().Select(nt => nt.Name));
                                return $"{nestedPrefix}Of{nestedArgs}";
                            }

                            return t.Name;
                        }));
                        return $"{prefix}Of{genericArgs}";
                    }

                    // Handle non-generic types
                    return type.Name;
                });
            }
        );
    }


    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        app.UseForwardedHeaders();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseAbpRequestLocalization();

        if (!env.IsDevelopment())
        {
            app.UseErrorPage();
            app.UseHsts();
        }

        app.UseCorrelationId();
        app.MapAbpStaticAssets();
        app.UseAbpStudioLink();
        app.UseRouting();
        app.UseAbpSecurityHeaders();
        app.UseSession(); // Required for deferred user synchronization
        app.UseAuthentication();

        // Add token refresh middleware
        app.UseMiddleware<TokenRefreshMiddleware>();

        // Add authentication debugging middleware
        app.Use(async (context, next) =>
        {
            var logger = context.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
            logger.LogInformation("Request path: {Path}", context.Request.Path);
            logger.LogInformation("User authenticated: {IsAuthenticated}", context.User?.Identity?.IsAuthenticated ?? false);
            if (context.User?.Identity?.IsAuthenticated == true)
            {
                logger.LogInformation("User name: {UserName}", context.User.Identity.Name);
                logger.LogInformation("Authentication type: {AuthType}", context.User.Identity.AuthenticationType);
            }
            await next();
        });

        // app.UseAbpOpenIddictValidation();

        // Add user synchronization middleware after authentication
        // app.UseMiddleware<UserSynchronizationMiddleware>();

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseUnitOfWork();
        app.UseDynamicClaims();
        app.UseAuthorization();
        app.UseInertia();
        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "JettyApproval API");
        });
        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseConfiguredEndpoints();
        app.UseConfiguredEndpoints(endpoints =>
        {
            endpoints.MapControllerRoute(
                name: "default",
                pattern: "{controller=Admin}/{action=Index}/{id?}");
        });
    }
}
