using Imip.JettyApproval.EntityFrameworkCore;
using Imip.JettyApproval.Localization;
using Imip.JettyApproval.MultiTenancy;
using Imip.JettyApproval.Web.Menus;
using Imip.JettyApproval.Web.Middleware;
using Imip.JettyApproval.Web.Modules;
using Imip.JettyApproval.Web.Services.Attachments;
using Imip.JettyApproval.Web.Services.Interfaces;
using Imip.JettyApproval.Web.Services;
using Imip.JettyApproval.Web.Swagger;
using InertiaCore.Extensions;
using Microsoft.AspNetCore.Antiforgery;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.HttpOverrides;
using Microsoft.AspNetCore.Server.Kestrel.Core;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.OpenApi.Models;
using OpenIddict.Validation;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using Volo.Abp;
using Volo.Abp.Account.Web;
using Volo.Abp.AspNetCore.Mvc;
using Volo.Abp.AspNetCore.Mvc.AntiForgery;
using Volo.Abp.AspNetCore.Mvc.Localization;
using Volo.Abp.AspNetCore.Mvc.UI.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.LeptonXLite.Bundling;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared;
using Volo.Abp.AspNetCore.Mvc.UI.Theme.Shared.Toolbars;
using Volo.Abp.AspNetCore.Serilog;
using Volo.Abp.Autofac;
using Volo.Abp.AutoMapper;
using Volo.Abp.Data;
using Volo.Abp.FeatureManagement;
using Volo.Abp.Guids;
using Volo.Abp.Identity;
using Volo.Abp.Identity.Web;
using Volo.Abp.Modularity;
using Volo.Abp.MultiTenancy;
using Volo.Abp.PermissionManagement;
using Volo.Abp.Security.Claims;
using Volo.Abp.Studio.Client.AspNetCore;
using Volo.Abp.Swashbuckle;
using Volo.Abp.TenantManagement.Web;
using Volo.Abp.UI.Navigation;
using Volo.Abp.UI.Navigation.Urls;
using Volo.Abp.VirtualFileSystem;
using Volo.Abp.Caching.StackExchangeRedis;
using Volo.Abp.Caching;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.Extensions.Caching.StackExchangeRedis;
using StackExchange.Redis;

namespace Imip.JettyApproval.Web;

[DependsOn(
    typeof(JettyApprovalHttpApiModule),
    typeof(JettyApprovalApplicationModule),
    typeof(JettyApprovalEntityFrameworkCoreModule),
    typeof(AbpAspNetCoreMvcModule),
    typeof(AbpAutofacModule),
    typeof(AbpStudioClientAspNetCoreModule),
    typeof(AbpIdentityWebModule),
    typeof(AbpAspNetCoreMvcUiLeptonXLiteThemeModule),
    typeof(AbpAccountWebModule),
    typeof(AbpTenantManagementWebModule),
    typeof(AbpFeatureManagementWebModule),
    typeof(AbpSwashbuckleModule),
    typeof(AbpAspNetCoreSerilogModule),
    typeof(PermissionCheckerModule),
    typeof(AbpCachingStackExchangeRedisModule)
)]
public class JettyApprovalWebModule : AbpModule
{
    public override void PreConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();

        context.Services.PreConfigure<AbpMvcDataAnnotationsLocalizationOptions>(options =>
        {
            options.AddAssemblyResource(
                typeof(JettyApprovalResource),
                typeof(JettyApprovalDomainModule).Assembly,
                typeof(JettyApprovalDomainSharedModule).Assembly,
                typeof(JettyApprovalApplicationModule).Assembly,
                typeof(JettyApprovalApplicationContractsModule).Assembly,
                typeof(JettyApprovalWebModule).Assembly
            );
        });
    }


    private void ConfigureDataProtection(ServiceConfigurationContext context, IConfiguration configuration, IWebHostEnvironment hostingEnvironment)
    {
        var appName = configuration["App:AppName"] ?? "Imip.JettyApproval";

        var dataProtectionBuilder = context.Services.AddDataProtection()
            .SetApplicationName(appName)
            .SetDefaultKeyLifetime(TimeSpan.FromDays(90));

        if (hostingEnvironment.IsDevelopment())
        {
            // Development: Local file system
            var keysPath = Path.Combine(hostingEnvironment.ContentRootPath, "App_Data", "DataProtection-Keys");
            Directory.CreateDirectory(keysPath);
            dataProtectionBuilder.PersistKeysToFileSystem(new DirectoryInfo(keysPath));
            Console.WriteLine($"Development: Data protection keys stored at: {keysPath}");
        }
        else
        {
            // Production/Kubernetes: Multiple storage strategies
            ConfigureProductionDataProtection(context, configuration, dataProtectionBuilder, appName);
        }
    }

    private void ConfigureProductionDataProtection(ServiceConfigurationContext context, IConfiguration configuration,
        IDataProtectionBuilder dataProtectionBuilder, string appName)
    {
        var redisIsEnabled = configuration.GetValue<bool>("Redis:IsEnabled", true);
        var redisConfiguration = configuration["Redis:Configuration"];

        context.Services.AddDataProtection()
            .PersistKeysToStackExchangeRedis(ConnectionMultiplexer.Connect(redisConfiguration), "DataProtection-Keys");

        // Strategy 1: Try Redis first (best for Kubernetes multi-pod scenarios)
        if (redisIsEnabled && !string.IsNullOrEmpty(redisConfiguration))
        {
            try
            {
                Configure<RedisCacheOptions>(options =>
                {
                    options.Configuration = redisConfiguration;
                    options.InstanceName = $"{appName}:DataProtection:";
                });
                Console.WriteLine("Production: Data protection keys stored in Redis (Kubernetes-ready)");

                Configure<AbpDistributedCacheOptions>(options =>
                {
                    options.KeyPrefix = $"{appName}:DataProtection:";
                });
                Console.WriteLine("Production: distributed cache stored in Redis (Kubernetes-ready)");
                return; // Redis success, we're done
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Redis data protection failed: {ex.Message}. Falling back to persistent volume.");
            }
        }

        // Strategy 2: Kubernetes Persistent Volume (fallback)
        ConfigureKubernetesPersistentStorage(dataProtectionBuilder);
    }

    private void ConfigureKubernetesPersistentStorage(IDataProtectionBuilder dataProtectionBuilder)
    {
        // Kubernetes persistent volume paths (must be configured in your deployment)
        var kubernetesPaths = new[]
        {
            "/app/data-protection-keys",  // Your current path
            "/data/keys",                 // Alternative common path
            "/persistent/data-protection-keys"  // Another alternative
        };

        foreach (var path in kubernetesPaths)
        {
            try
            {
                // Ensure directory exists
                Directory.CreateDirectory(path);

                // Test write permissions
                var testFile = Path.Combine(path, "test-write.tmp");
                File.WriteAllText(testFile, "test");
                File.Delete(testFile);

                dataProtectionBuilder.PersistKeysToFileSystem(new DirectoryInfo(path));
                Console.WriteLine($"Production: Data protection keys stored at: {path} (Kubernetes PV)");
                return; // Success
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Failed to use path {path}: {ex.Message}");
            }
        }

        // Strategy 3: Fallback to temporary storage (NOT recommended for production)
        Console.WriteLine("WARNING: Using temporary storage for data protection keys. Users will be logged out on pod restarts!");
        var tempPath = Path.Combine(Path.GetTempPath(), "dataprotection-keys");
        Directory.CreateDirectory(tempPath);
        dataProtectionBuilder.PersistKeysToFileSystem(new DirectoryInfo(tempPath));
    }

    // Add this method to handle distributed cache configuration
    private void ConfigureDistributedCache(ServiceConfigurationContext context, IConfiguration configuration)
    {
        var redisIsEnabled = configuration.GetValue<bool>("Redis:IsEnabled", true);
        var redisConfiguration = configuration["Redis:Configuration"];

        Configure<AbpDistributedCacheOptions>(options =>
        {
            options.KeyPrefix = "Imip.JettyApproval.Cache:";
            options.GlobalCacheEntryOptions.AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(24);
            options.GlobalCacheEntryOptions.SlidingExpiration = TimeSpan.FromHours(1);
        });

        if (redisIsEnabled && !string.IsNullOrEmpty(redisConfiguration))
        {
            try
            {
                // Redis cache will be configured by AbpCachingStackExchangeRedisModule
                Console.WriteLine("ABP distributed cache configured to use Redis");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Redis cache configuration failed: {ex.Message}. Using in-memory cache.");
            }
        }
        else
        {
            Console.WriteLine("Using in-memory distributed cache");
        }
    }

    private void ConfigureAntiforgery(ServiceConfigurationContext context, IConfiguration configuration, IWebHostEnvironment hostingEnvironment)
    {
        // Configure ABP antiforgery options
        Configure<AbpAntiForgeryOptions>(options =>
        {
            options.TokenCookie.Expiration = TimeSpan.FromDays(365);
            // In development, allow non-HTTPS for easier debugging
            if (hostingEnvironment.IsDevelopment())
            {
                options.TokenCookie.SecurePolicy = CookieSecurePolicy.SameAsRequest;
            }
        });

        // Configure ASP.NET Core antiforgery options
        context.Services.Configure<AntiforgeryOptions>(options =>
        {
            options.Cookie.Name = ".Imip.JettyApproval.Antiforgery";
            options.Cookie.HttpOnly = true;

            var requireHttps = !hostingEnvironment.IsDevelopment() &&
                              configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", true);

            options.Cookie.SecurePolicy = requireHttps ? CookieSecurePolicy.Always : CookieSecurePolicy.SameAsRequest;
            options.Cookie.SameSite = SameSiteMode.Lax;
            options.Cookie.MaxAge = TimeSpan.FromHours(2); // Longer lifetime for development
            options.FormFieldName = "__RequestVerificationToken";
            options.HeaderName = "X-CSRF-TOKEN";
        });
    }

    public override void ConfigureServices(ServiceConfigurationContext context)
    {
        var hostingEnvironment = context.Services.GetHostingEnvironment();
        var configuration = context.Services.GetConfiguration();
        var appName = configuration["App:AppName"] ?? "Imip.IdentityServer";

        // Add this configuration for handling larger headers
        //context.Services.Configure<IISServerOptions>(static options =>
        //{
        //    options.MaxRequestHeadersTotalSize = 65536; // Increased header size limit (64KB)
        //});

        // For Kestrel
        context.Services.Configure<KestrelServerOptions>(options =>
        {
            options.Limits.MaxRequestHeadersTotalSize = 65536; // Increased header size limit (64KB)
        });

        ConfigureDataProtection(context, configuration, hostingEnvironment);
        ConfigureDistributedCache(context, configuration);

        if (!configuration.GetValue<bool>("App:DisablePII"))
        {
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.ShowPII = true;
            Microsoft.IdentityModel.Logging.IdentityModelEventSource.LogCompleteSecurityArtifact = true;
        }

        if (!configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata"))
        {
            // Remove OpenIddict server options since we're not using OpenIddict as a server
            // Configure<OpenIddictServerAspNetCoreOptions>(options =>
            // {
            //     options.DisableTransportSecurityRequirement = true;
            // });

            Configure<ForwardedHeadersOptions>(options =>
            {
                options.ForwardedHeaders = ForwardedHeaders.XForwardedProto;
            });
        }

        ConfigureAntiforgery(context, configuration, hostingEnvironment);

        context.Services.AddInertia(options =>
        {
            options.RootView = "~/Views/App.cshtml";
        });

        context.Services.AddViteHelper(options =>
        {
            options.PublicDirectory = "wwwroot";
            options.ManifestFilename = "manifest.json";
            options.BuildDirectory = "build";
        });

        ConfigureBundles();
        ConfigureUrls(configuration);
        ConfigureHealthChecks(context);
        ConfigureAuthentication(context, configuration);
        ConfigureAutoMapper();
        ConfigureVirtualFileSystem(hostingEnvironment);
        ConfigureNavigationServices();
        ConfigureAutoApiControllers();
        SwaggerConfigurationService.ConfigureSwagger(context.Services);

        Configure<PermissionManagementOptions>(options =>
        {
            options.IsDynamicPermissionStoreEnabled = true;
        });

        // Register the HttpContextAccessor if it's not already registered
        context.Services.AddHttpContextAccessor();

        // Register HttpClientFactory for testing and other HTTP operations
        context.Services.AddHttpClient();

        // Register token service
        context.Services.AddScoped<ITokenService, TokenService>();
        context.Services.AddScoped<IAuthenticationTokenValidationService, AuthenticationTokenValidationService>();
        context.Services.AddScoped<ExternalApiService>();
        context.Services.AddScoped<AppToAppService>();

        // Register background token refresh service
        context.Services.AddHostedService<BackgroundTokenRefreshService>();

        // Configure session for deferred user synchronization
        context.Services.AddSession(options =>
        {
            options.IdleTimeout = TimeSpan.FromMinutes(30);
            options.Cookie.HttpOnly = true;
            options.Cookie.IsEssential = true;
            options.Cookie.Name = ".Imip.JettyApproval.Session";
        });
    }

    private void ConfigureHealthChecks(ServiceConfigurationContext context)
    {
        context.Services.AddHealthChecks();
    }

    private void ConfigureBundles()
    {
        Configure<AbpBundlingOptions>(options =>
        {
            options.StyleBundles.Configure(
                LeptonXLiteThemeBundles.Styles.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-styles.css");
                }
            );

            options.ScriptBundles.Configure(
                LeptonXLiteThemeBundles.Scripts.Global,
                bundle =>
                {
                    bundle.AddFiles("/global-scripts.js");
                }
            );
        });
    }

    private void ConfigureUrls(IConfiguration configuration)
    {
        Configure<AppUrlOptions>(options =>
        {
            options.Applications["MVC"].RootUrl = configuration["App:SelfUrl"];
        });
    }

    private void ConfigureAuthentication(ServiceConfigurationContext context, IConfiguration configuration)
    {
        context.Services.AddAuthentication(options =>
        {
            options.DefaultScheme = "Cookies";
            options.DefaultChallengeScheme = "oidc";
            options.DefaultSignOutScheme = "oidc";
        })
        .AddCookie("Cookies", options =>
        {
            options.ExpireTimeSpan = TimeSpan.FromMinutes(60);
            options.SlidingExpiration = true;
            options.Cookie.Name = ".Imip.JettyApproval.Auth";
            options.Cookie.SameSite = SameSiteMode.Lax;
            options.LoginPath = "/Account/Login";
            options.LogoutPath = "/Account/Logout";

            // Add debugging for cookie events
            options.Events.OnRedirectToLogin = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                return Task.CompletedTask;
            };

            options.Events.OnRedirectToAccessDenied = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                return Task.CompletedTask;
            };
        })
        .AddJwtBearer("Bearer", options =>
        {
            // Configure JWT Bearer for validating tokens from other apps
            options.Authority = configuration["AuthServer:Authority"];
            options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", false);
            options.Audience = configuration["AuthServer:ClientId"]; // Use this app's client ID as audience

            // Configure token validation parameters
            options.TokenValidationParameters = new Microsoft.IdentityModel.Tokens.TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = configuration["AuthServer:Authority"],
                ValidAudience = configuration["AuthServer:ClientId"],
                ClockSkew = TimeSpan.FromMinutes(5)
            };

            // Handle JWT events for debugging and custom validation
            options.Events = new JwtBearerEvents
            {
                OnTokenValidated = async context =>
                {
                    var tokenValidationService = context.HttpContext.RequestServices
                        .GetRequiredService<IAuthenticationTokenValidationService>();
                    await tokenValidationService.ValidateJwtBearerTokenAsync(context);
                },
                OnAuthenticationFailed = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                    logger.LogError("JWT authentication failed: {Exception}", context.Exception);
                    return Task.CompletedTask;
                },
                OnChallenge = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                    return Task.CompletedTask;
                }
            };
        })
        .AddOpenIdConnect("oidc", options =>
        {
            // Use consistent configuration keys - pick one set
            options.Authority = configuration["AuthServer:Authority"] ?? configuration["OpenIdConnect:Authority"];
            options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", false);

            options.ClientId = configuration["AuthServer:ClientId"] ?? configuration["OpenIdConnect:ClientId"];
            options.ClientSecret = configuration["AuthServer:ClientSecret"] ?? configuration["OpenIdConnect:ClientSecret"];

            // Configure redirect URIs
            options.SignedOutRedirectUri = configuration["OpenIdConnect:SignedOutRedirectUri"] ?? "http://localhost:5000";
            options.SignedOutCallbackPath = "/signout-callback-oidc";
            options.RemoteSignOutPath = "/signout-oidc";

            // Configure sign-in scheme to use cookies
            options.SignInScheme = "Cookies";

            // Use authorization code flow with PKCE for security
            options.ResponseType = "code";
            options.UsePkce = false;

            // Basic scopes
            options.Scope.Clear();
            options.Scope.Add("openid");
            options.Scope.Add("profile");
            options.Scope.Add("email");

            // Save tokens and get claims from UserInfo
            options.SaveTokens = true;
            options.GetClaimsFromUserInfoEndpoint = true;

            // Configure token refresh
            options.RefreshInterval = TimeSpan.FromMinutes(30); // Refresh every 30 minutes
            options.UseTokenLifetime = true; // Use token lifetime from identity server

            // Map claims properly
            options.ClaimActions.MapJsonKey(ClaimTypes.NameIdentifier, "sub");
            options.ClaimActions.MapJsonKey(ClaimTypes.Name, "name");
            options.ClaimActions.MapJsonKey(ClaimTypes.Email, "email");
            options.ClaimActions.MapJsonKey(ClaimTypes.Role, "role");

            // Handle events
            options.Events.OnTokenValidated = async context =>
            {
                var tokenValidationService = context.HttpContext.RequestServices
                    .GetRequiredService<IAuthenticationTokenValidationService>();
                await tokenValidationService.ValidateOpenIdConnectTokenAsync(context);
            };

            // Handle sign-in errors
            options.Events.OnRemoteFailure = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                logger.LogError("Remote authentication failure: {Error}", context.Failure?.Message);

                context.Response.Redirect("/Account/Login?error=" + Uri.EscapeDataString(context.Failure?.Message ?? "Authentication failed"));
                context.HandleResponse();
                return Task.CompletedTask;
            };

            // Handle tenant information in redirect (if using multi-tenancy)
            options.Events.OnRedirectToIdentityProvider = context =>
            {
                var currentTenant = context.HttpContext.RequestServices.GetRequiredService<ICurrentTenant>();

                if (currentTenant.Id.HasValue && !string.IsNullOrEmpty(currentTenant.Name))
                {
                    context.ProtocolMessage.SetParameter("tenant", currentTenant.Name);
                }

                return Task.CompletedTask;
            };

            // Handle authentication errors
            options.Events.OnAuthenticationFailed = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                logger.LogError(context.Exception, "OIDC authentication failed");
                return Task.CompletedTask;
            };

            // Ensure user is signed in to the cookie scheme
            options.Events.OnTicketReceived = async context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                logger.LogInformation("OIDC ticket received successfully");

                // Log authentication details
                if (context.Principal?.Identity?.IsAuthenticated == true)
                {
                    logger.LogInformation("User is authenticated: {UserName}", context.Principal.Identity.Name);
                    logger.LogInformation("Authentication type: {AuthType}", context.Principal.Identity.AuthenticationType);

                    // Log all claims for debugging
                    foreach (var claim in context.Principal.Claims)
                    {
                        logger.LogInformation("Claim: {Type} = {Value}", claim.Type, claim.Value);
                    }

                    logger.LogInformation("OIDC authentication completed successfully");
                }
                else
                {
                    logger.LogWarning("User is not authenticated after ticket received");
                }
            };

            // Add more event handlers for debugging
            options.Events.OnAuthorizationCodeReceived = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                logger.LogInformation("Authorization code received");
                return Task.CompletedTask;
            };

            options.Events.OnTokenResponseReceived = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                logger.LogInformation("Token response received");
                return Task.CompletedTask;
            };

            options.Events.OnUserInformationReceived = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
                logger.LogInformation("User information received");
                return Task.CompletedTask;
            };
        });

        context.Services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = true;
        });

        context.Services.Configure<JwtBearerOptions>(options =>
        {
            options.Authority = configuration["AuthServer:Authority"];
            options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata");
            options.Audience = "JettyApproval";
        });
    }

    private void ConfigureAutoMapper()
    {
        Configure<AbpAutoMapperOptions>(options =>
        {
            options.AddMaps<JettyApprovalWebModule>();
        });
    }

    private static bool IsRunningInContainer()
    {
        // Check for container-specific environment indicators
        return File.Exists("/.dockerenv") ||
               (File.Exists("/proc/1/cgroup") && File.ReadAllText("/proc/1/cgroup").Contains("/docker/"));
    }

    private void ConfigureVirtualFileSystem(IWebHostEnvironment hostingEnvironment)
    {
        Configure<AbpVirtualFileSystemOptions>(options =>
        {
            options.FileSets.AddEmbedded<JettyApprovalWebModule>();

            // Only replace embedded resources with physical files in a development environment,
            // and only if we're not running in a container
            if (hostingEnvironment.IsDevelopment() && !IsRunningInContainer())
            {
                try
                {
                    var domainSharedPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Domain.Shared", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainSharedPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalDomainSharedModule>(domainSharedPath);
                    }

                    var domainPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Domain", Path.DirectorySeparatorChar));
                    if (Directory.Exists(domainPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalDomainModule>(domainPath);
                    }

                    var contractsPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Application.Contracts", Path.DirectorySeparatorChar));
                    if (Directory.Exists(contractsPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalApplicationContractsModule>(
                            contractsPath);
                    }

                    var appPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}Imip.JettyApproval.Application", Path.DirectorySeparatorChar));
                    if (Directory.Exists(appPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalApplicationModule>(appPath);
                    }

                    var httpApiPath = Path.Combine(hostingEnvironment.ContentRootPath,
                        string.Format("..{0}..{0}src{0}Imip.JettyApproval.HttpApi", Path.DirectorySeparatorChar));
                    if (Directory.Exists(httpApiPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalHttpApiModule>(httpApiPath);
                    }

                    if (Directory.Exists(hostingEnvironment.ContentRootPath))
                    {
                        options.FileSets.ReplaceEmbeddedByPhysical<JettyApprovalWebModule>(hostingEnvironment
                            .ContentRootPath);
                    }
                }
                catch (Exception ex)
                {
                    // Log the exception but continue without replacing embedded resources
                    Console.WriteLine($"Error configuring virtual file system: {ex.Message}");
                }
            }
        });
    }

    private void ConfigureNavigationServices()
    {
        Configure<AbpNavigationOptions>(options =>
        {
            options.MenuContributors.Add(new JettyApprovalMenuContributor());
        });

        Configure<AbpToolbarOptions>(options =>
        {
            options.Contributors.Add(new JettyApprovalToolbarContributor());
        });
    }

    private void ConfigureAutoApiControllers()
    {
        Configure<AbpAspNetCoreMvcOptions>(options =>
        {
            options.ConventionalControllers.Create(typeof(JettyApprovalApplicationModule).Assembly);
        });
    }

    public override void OnApplicationInitialization(ApplicationInitializationContext context)
    {
        var app = context.GetApplicationBuilder();
        var env = context.GetEnvironment();

        app.UseForwardedHeaders();

        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }

        app.UseAbpRequestLocalization();

        if (!env.IsDevelopment())
        {
            app.UseErrorPage();
            app.UseHsts();
        }

        app.UseCorrelationId();
        app.MapAbpStaticAssets();
        app.UseAbpStudioLink();
        app.UseRouting();
        app.UseAbpSecurityHeaders();
        app.UseSession(); // Required for deferred user synchronization
        app.UseAuthentication();

        // Add token refresh middleware
        app.UseMiddleware<TokenRefreshMiddleware>();

        // Add authentication debugging middleware
        app.Use(async (context, next) =>
        {
            var logger = context.RequestServices.GetRequiredService<ILogger<JettyApprovalWebModule>>();
            logger.LogInformation("Request path: {Path}", context.Request.Path);
            logger.LogInformation("User authenticated: {IsAuthenticated}", context.User?.Identity?.IsAuthenticated ?? false);
            if (context.User?.Identity?.IsAuthenticated == true)
            {
                logger.LogInformation("User name: {UserName}", context.User.Identity.Name);
                logger.LogInformation("Authentication type: {AuthType}", context.User.Identity.AuthenticationType);
            }
            await next();
        });

        // app.UseAbpOpenIddictValidation();

        // Add user synchronization middleware after authentication
        // app.UseMiddleware<UserSynchronizationMiddleware>();

        if (MultiTenancyConsts.IsEnabled)
        {
            app.UseMultiTenancy();
        }

        app.UseUnitOfWork();
        app.UseDynamicClaims();
        app.UseAuthorization();
        app.UseInertia();
        app.UseSwagger();
        app.UseAbpSwaggerUI(options =>
        {
            options.SwaggerEndpoint("/swagger/v1/swagger.json", "JettyApproval API");
        });
        app.UseAuditing();
        app.UseAbpSerilogEnrichers();
        app.UseConfiguredEndpoints();
        app.UseConfiguredEndpoints(endpoints =>
        {
            endpoints.MapControllerRoute(
                name: "default",
                pattern: "{controller=Admin}/{action=Index}/{id?}");
        });
    }
}
