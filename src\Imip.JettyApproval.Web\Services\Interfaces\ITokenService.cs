using System.Threading.Tasks;

namespace Imip.JettyApproval.Web.Services.Interfaces;

public interface ITokenService
{
    /// <summary>
    /// Gets the current user's access token
    /// </summary>
    Task<string> GetAccessTokenAsync();

    /// <summary>
    /// Gets the current user's refresh token
    /// </summary>
    Task<string> GetRefreshTokenAsync();

    /// <summary>
    /// Gets a valid access token, refreshing if necessary
    /// </summary>
    Task<string> GetValidAccessTokenAsync();

    /// <summary>
    /// Refreshes the access token using the refresh token
    /// </summary>
    Task<string> RefreshAccessTokenAsync();

    /// <summary>
    /// Checks if the current user has a valid access token
    /// </summary>
    Task<bool> HasValidTokenAsync();

    /// <summary>
    /// Checks if refresh token is expired
    /// </summary>
    Task<bool> IsRefreshTokenExpiredAsync();
}