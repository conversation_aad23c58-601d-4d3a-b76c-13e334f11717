using System;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Volo.Abp.MultiTenancy;
using Volo.Abp.Security.Claims;
using Imip.JettyApproval.Web.Services.Interfaces;

namespace Imip.JettyApproval.Web.Services;

public static class AuthenticationConfigurationService
{
    /// <summary>
    /// Configures authentication services including Cookies, JWT Bearer, and OpenID Connect
    /// </summary>
    public static void ConfigureAuthentication(IServiceCollection services, IConfiguration configuration)
    {
        var authBuilder = services.AddAuthentication(options =>
        {
            options.DefaultScheme = "Cookies";
            options.DefaultChallengeScheme = "oidc";
            options.DefaultSignOutScheme = "oidc";
        });

        ConfigureCookieAuthentication(authBuilder);
        ConfigureJwtBearerAuthentication(authBuilder, configuration);
        ConfigureOpenIdConnectAuthentication(authBuilder, configuration);
        ConfigureAbpClaimsOptions(services);
        ConfigureJwtBearerOptions(services, configuration);
    }

    private static void ConfigureCookieAuthentication(AuthenticationBuilder authBuilder)
    {
        authBuilder.AddCookie("Cookies", options =>
        {
            options.ExpireTimeSpan = TimeSpan.FromMinutes(60);
            options.SlidingExpiration = true;
            options.Cookie.Name = ".Imip.JettyApproval.Auth";
            options.Cookie.SameSite = SameSiteMode.Lax;
            options.LoginPath = "/Account/Login";
            options.LogoutPath = "/Account/Logout";

            // Add debugging for cookie events
            options.Events.OnRedirectToLogin = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
                logger.LogDebug("Cookie authentication redirecting to login");
                return Task.CompletedTask;
            };

            options.Events.OnRedirectToAccessDenied = context =>
            {
                var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
                logger.LogWarning("Cookie authentication access denied for path: {Path}", context.Request.Path);
                return Task.CompletedTask;
            };
        });
    }

    private static void ConfigureJwtBearerAuthentication(AuthenticationBuilder authBuilder, IConfiguration configuration)
    {
        authBuilder.AddJwtBearer("Bearer", options =>
        {
            // Configure JWT Bearer for validating tokens from other apps
            options.Authority = configuration["AuthServer:Authority"];
            options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", false);
            options.Audience = configuration["AuthServer:ClientId"]; // Use this app's client ID as audience

            // Configure token validation parameters
            options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ValidateIssuerSigningKey = true,
                ValidIssuer = configuration["AuthServer:Authority"],
                ValidAudience = configuration["AuthServer:ClientId"],
                ClockSkew = TimeSpan.FromMinutes(5)
            };

            // Handle JWT events for debugging and custom validation
            options.Events = new JwtBearerEvents
            {
                OnTokenValidated = async context =>
                {
                    var tokenValidationService = context.HttpContext.RequestServices
                        .GetRequiredService<IAuthenticationTokenValidationService>();
                    await tokenValidationService.ValidateJwtBearerTokenAsync(context);
                },
                OnAuthenticationFailed = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
                    logger.LogError("JWT authentication failed: {Exception}", context.Exception);
                    return Task.CompletedTask;
                },
                OnChallenge = context =>
                {
                    var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
                    logger.LogDebug("JWT authentication challenge initiated");
                    return Task.CompletedTask;
                }
            };
        });
    }

    private static void ConfigureOpenIdConnectAuthentication(AuthenticationBuilder authBuilder, IConfiguration configuration)
    {
        authBuilder.AddOpenIdConnect("oidc", options =>
        {
            ConfigureOpenIdConnectBasicSettings(options, configuration);
            ConfigureOpenIdConnectScopes(options);
            ConfigureOpenIdConnectClaims(options);
            ConfigureOpenIdConnectEvents(options);
        });
    }

    private static void ConfigureOpenIdConnectBasicSettings(OpenIdConnectOptions options, IConfiguration configuration)
    {
        // Use consistent configuration keys - pick one set
        options.Authority = configuration["AuthServer:Authority"] ?? configuration["OpenIdConnect:Authority"];
        options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata", false);

        options.ClientId = configuration["AuthServer:ClientId"] ?? configuration["OpenIdConnect:ClientId"];
        options.ClientSecret = configuration["AuthServer:ClientSecret"] ?? configuration["OpenIdConnect:ClientSecret"];

        // Configure redirect URIs
        options.SignedOutRedirectUri = configuration["OpenIdConnect:SignedOutRedirectUri"] ?? "http://localhost:5000";
        options.SignedOutCallbackPath = "/signout-callback-oidc";
        options.RemoteSignOutPath = "/signout-oidc";

        // Configure sign-in scheme to use cookies
        options.SignInScheme = "Cookies";

        // Use authorization code flow with PKCE for security
        options.ResponseType = "code";
        options.UsePkce = false;

        // Save tokens and get claims from UserInfo
        options.SaveTokens = true;
        options.GetClaimsFromUserInfoEndpoint = true;

        // Configure token refresh
        options.RefreshInterval = TimeSpan.FromMinutes(30); // Refresh every 30 minutes
        options.UseTokenLifetime = true; // Use token lifetime from identity server
    }

    private static void ConfigureOpenIdConnectScopes(OpenIdConnectOptions options)
    {
        // Basic scopes
        options.Scope.Clear();
        options.Scope.Add("openid");
        options.Scope.Add("profile");
        options.Scope.Add("email");
    }

    private static void ConfigureOpenIdConnectClaims(OpenIdConnectOptions options)
    {
        // Map claims properly
        options.ClaimActions.MapJsonKey(ClaimTypes.NameIdentifier, "sub");
        options.ClaimActions.MapJsonKey(ClaimTypes.Name, "name");
        options.ClaimActions.MapJsonKey(ClaimTypes.Email, "email");
        options.ClaimActions.MapJsonKey(ClaimTypes.Role, "role");
    }

    private static void ConfigureOpenIdConnectEvents(OpenIdConnectOptions options)
    {
        // Handle token validation
        options.Events.OnTokenValidated = async context =>
        {
            var tokenValidationService = context.HttpContext.RequestServices
                .GetRequiredService<IAuthenticationTokenValidationService>();
            await tokenValidationService.ValidateOpenIdConnectTokenAsync(context);
        };

        // Handle sign-in errors
        options.Events.OnRemoteFailure = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
            logger.LogError("Remote authentication failure: {Error}", context.Failure?.Message);

            context.Response.Redirect("/Account/Login?error=" + Uri.EscapeDataString(context.Failure?.Message ?? "Authentication failed"));
            context.HandleResponse();
            return Task.CompletedTask;
        };

        // Handle tenant information in redirect (if using multi-tenancy)
        options.Events.OnRedirectToIdentityProvider = context =>
        {
            var currentTenant = context.HttpContext.RequestServices.GetRequiredService<ICurrentTenant>();

            if (currentTenant.Id.HasValue && !string.IsNullOrEmpty(currentTenant.Name))
            {
                context.ProtocolMessage.SetParameter("tenant", currentTenant.Name);
            }

            return Task.CompletedTask;
        };

        // Handle authentication errors
        options.Events.OnAuthenticationFailed = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
            logger.LogError(context.Exception, "OIDC authentication failed");
            return Task.CompletedTask;
        };

        ConfigureOpenIdConnectDebuggingEvents(options);
    }

    private static void ConfigureOpenIdConnectDebuggingEvents(OpenIdConnectOptions options)
    {
        // Ensure user is signed in to the cookie scheme
        options.Events.OnTicketReceived = async context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
            logger.LogInformation("OIDC ticket received successfully");

            // Log authentication details
            if (context.Principal?.Identity?.IsAuthenticated == true)
            {
                logger.LogInformation("User is authenticated: {UserName}", context.Principal.Identity.Name);
                logger.LogInformation("Authentication type: {AuthType}", context.Principal.Identity.AuthenticationType);

                // Log all claims for debugging
                foreach (var claim in context.Principal.Claims)
                {
                    logger.LogInformation("Claim: {Type} = {Value}", claim.Type, claim.Value);
                }

                logger.LogInformation("OIDC authentication completed successfully");
            }
            else
            {
                logger.LogWarning("User is not authenticated after ticket received");
            }
        };

        // Add more event handlers for debugging
        options.Events.OnAuthorizationCodeReceived = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
            logger.LogInformation("Authorization code received");
            return Task.CompletedTask;
        };

        options.Events.OnTokenResponseReceived = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
            logger.LogInformation("Token response received");
            return Task.CompletedTask;
        };

        options.Events.OnUserInformationReceived = context =>
        {
            var logger = context.HttpContext.RequestServices.GetRequiredService<ILogger<object>>();
            logger.LogInformation("User information received");
            return Task.CompletedTask;
        };
    }

    private static void ConfigureAbpClaimsOptions(IServiceCollection services)
    {
        services.Configure<AbpClaimsPrincipalFactoryOptions>(options =>
        {
            options.IsDynamicClaimsEnabled = true;
        });
    }

    private static void ConfigureJwtBearerOptions(IServiceCollection services, IConfiguration configuration)
    {
        services.Configure<JwtBearerOptions>(options =>
        {
            options.Authority = configuration["AuthServer:Authority"];
            options.RequireHttpsMetadata = configuration.GetValue<bool>("AuthServer:RequireHttpsMetadata");
            options.Audience = "JettyApproval";
        });
    }
}
